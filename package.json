{"name": "all-aware", "private": true, "sideEffects": false, "version": "1.0.1", "type": "module", "scripts": {"shopify": "shopify", "build": "shopify hydrogen build", "deploy:h2": "shopify hydrogen deploy", "dev": "shopify app dev", "dev:h2": "shopify hydrogen dev --debug", "preview": "shopify hydrogen preview --build", "deploy": "node scripts/deploy.cjs", "tunnel": "ngrok http", "lint": "eslint --fix --no-error-on-unmatched-pattern --ext .js,.ts,.jsx,.tsx .", "typecheck": "tsc --noEmit", "codegen": "shopify hydrogen codegen", "icons": "npx @svgr/cli -- app/icons", "format": "prettier --write \"./**/*.{js,jsx,ts,tsx}\"", "pull:env": "npx shopify hydrogen env pull", "test": "jest", "prepare": "husky install", "postinstall": "node ./scripts/pre-commit-setup/index.cjs && npx shopify hydrogen shortcut && patch-package"}, "dependencies": {"@abraham/reflection": "^0.12.0", "@brandboostinggmbh/shopify-app-react-router": "^1.0.0", "@builder.io/sdk-react": "^4.2.1", "@formkit/auto-animate": "^0.8.2", "@google/generative-ai": "^0.24.1", "@gsap/react": "^2.1.2", "@hookform/resolvers": "^3.9.0", "@mjackson/form-data-parser": "^0.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@shopify/app-bridge-react": "^4.1.5", "@shopify/hydrogen": "^2025.5.0", "@shopify/polaris": "^13.9.0", "@shopify/remix-oxygen": "^3.0.0", "@shopify/shopify-app-session-storage-memory": "^4.0.17", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.8.0", "@tailwindcss/forms": "^0.5.9", "classnames": "^2.3.2", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "embla-carousel-react": "^8.5.2", "embla-carousel-wheel-gestures": "^8.0.1", "esbuild-plugin-polyfill-node": "^0.3.0", "graphql": "^16.6.0", "graphql-tag": "^2.12.6", "gsap": "^3.12.7", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "input-otp": "^1.2.5", "isbot": "^3.6.6", "jose": "^6.0.7", "lucide-react": "^0.511.0", "qr-scanner": "^1.4.2", "react": "^18.3.1", "react-compiler-runtime": "^19.0.0-beta-af1b7da-20250417", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-i18next": "^14.0.0", "react-number-format": "^5.4.2", "react-player": "^2.16.0", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "remix-hook-form": "^7.0.1", "stripe": "^17.2.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tsyringe": "^4.8.0", "ua-parser-js": "^2.0.3", "vaul": "^1.1.2", "vm": "^0.1.0", "zod": "^3.25.20"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@builder.io/dev-tools": "^1.1.41", "@graphql-codegen/cli": "^5.0.6", "@react-router/dev": "^7.6.0", "@react-router/fs-routes": "^7.6.0", "@shopify/api-codegen-preset": "^1.1.1", "@shopify/cli": "^3.80.4", "@shopify/hydrogen-codegen": "^0.3.3", "@shopify/mini-oxygen": "^3.2.1", "@shopify/oxygen-workers-types": "^4.1.6", "@shopify/prettier-config": "^1.1.2", "@svgr/cli": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@swc/helpers": "^0.5.15", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@total-typescript/ts-reset": "^0.4.2", "@types/eslint": "^8.4.10", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.5.1", "babel-plugin-react-compiler": "^19.0.0-beta-af1b7da-20250417", "class-variance-authority": "^0.7.1", "eslint": "^8.20.0", "eslint-plugin-hydrogen": "0.12.2", "eslint-plugin-react-compiler": "^19.0.0-beta-af1b7da-20250417", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^15.2.0", "patch-package": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "ts-jest": "^29.1.1", "tw-animate-css": "^1.3.0", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-babel": "^1.3.1", "vite-plugin-node-polyfills": "^0.23.0", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=20.18.1"}, "browserslist": ["defaults"], "workspaces": ["extensions/*"]}