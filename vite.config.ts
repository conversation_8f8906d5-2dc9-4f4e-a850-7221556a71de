import { defineConfig } from 'vite';
import { builderDevTools } from '@builder.io/dev-tools/vite';
import { hydrogen } from '@shopify/hydrogen/vite';
import { oxygen } from '@shopify/mini-oxygen/vite';
import { reactRouter } from '@react-router/dev/vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import { polyfillNode } from 'esbuild-plugin-polyfill-node';
// import swc from 'vite-plugin-swc-transform';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    tailwindcss(),
    react({
      babel: {
        plugins: [
          ['babel-plugin-react-compiler', { target: '18' }], // Or your React target
        ],
      },
    }),
    // swc({
    //   swcOptions: {
    //     jsc: {
    //       target: 'es2022',
    //       transform: {
    //         legacyDecorator: true,
    //         decoratorMetadata: true,
    //         useDefineForClassFields: false,
    //         react: {
    //           runtime: 'automatic',
    //         },
    //       },
    //       externalHelpers: true,
    //     },
    //   },
    // }),
    polyfillNode({
      polyfills: {
        fs: true,
        path: true,
      },
    }),
    builderDevTools(),
    hydrogen(),
    oxygen(),
    reactRouter(),
    tsconfigPaths(),
  ],
  build: {
    // Allow a strict Content-Security-Policy
    // withtout inlining assets as base64:
    assetsInlineLimit: 0,
  },
  server: {
    allowedHosts: ['localhost', '127.0.0.1', 'allaware-hydrogen-cs.ngrok.io', 'allawaredev-jm.ngrok.io'],
  },
  ssr: {
    external: ['fs', 'path'],
    optimizeDeps: {
      exclude: ['@shopify/polaris'],
      include: [
        'react-player/youtube',
        'react-player',
        'zod',
        'react',
        'react-fast-compare',
        'deepmerge',
        'use-sync-external-store/shim',
        'prop-types',
        'classnames',
        'qs',
        'semver',
        'set-cookie-parser',
        'vm',
        'cookie',
        'void-elements',
        '@shopify/admin-api-client',
        '@shopify/graphql-client',
        '@shopify/shopify-app-session-storage-memory',
        '@brandboostinggmbh/shopify-app-react-router/server',
        '@brandboostinggmbh/shopify-app-react-router/react',
        'i18next',
        'react-i18next',
        'gsap',
        'gsap/Flip',
        '@gsap/react',
        'gsap/ScrollTrigger',
        '@builder.io/sdk-react',
        '@radix-ui/react-toast',
        'class-variance-authority',
        'lucide-react',
        'clsx',
        'tailwind-merge',
        '@radix-ui/react-slot',
        '@radix-ui/react-separator',
        '@radix-ui/react-visually-hidden',
        '@radix-ui/react-tooltip',
        '@radix-ui/react-dialog',
        'remix-hook-form',
        'react-number-format',
        '@radix-ui/react-accordion',
        '@hookform/resolvers/zod',
        'react-hook-form',
        '@radix-ui/react-avatar',
        'embla-carousel-react',
        '@radix-ui/react-label',
        '@radix-ui/react-select',
      ],
    },
  },
});
