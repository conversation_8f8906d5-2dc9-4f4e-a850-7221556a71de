// Enable tSyringe
import '@abraham/reflection';
// @ts-ignore
// Virtual entry point for the app
import * as remixBuild from 'virtual:react-router/server-build';
import { storefrontRedirect } from '@shopify/hydrogen';
import { createRequestHandler } from '@shopify/remix-oxygen';
import { createAppLoadContext } from '@/lib/context';
import { SessionClient } from '@/business/clients/session-client';
import { StorefrontClient } from '@/business/clients/storefront-client';
import { CustomerAccountClient } from '@/business/clients/accounts-client';

/**
 * Export a fetch handler in module format.
 */
export default {
  async fetch(request, env, executionContext) {
    try {
      const now = Date.now();
      const context = await createAppLoadContext(request, env, executionContext);
      const session = context.resolve(SessionClient);
      const storefront = context.resolve(StorefrontClient);
      const customerAccount = context.resolve(CustomerAccountClient);

      context.session = session;
      context.storefront = storefront;
      context.customerAccount = customerAccount;
      /**
       * Create a Remix request handler and pass
       * Hydrogen's Storefront client to the loader context.
       */
      const handleRequest = createRequestHandler({
        build: remixBuild,
        mode: process.env.NODE_ENV,
        getLoadContext: () => context,
      });

      const response = await handleRequest(request);

      if (session.isPending) {
        response.headers.set('Set-Cookie', await session.commit());
      }

      response.headers.append(
        'Server-Timing',
        `hydrogen;desc="Full hydrogen process from beginning to end of fetch function in server.ts";dur=${
          Date.now() - now
        }`,
      );

      if (response.status === 404) {
        /**
         * Check for redirects only when there's a 404 from the app.
         * If the redirect doesn't exist, then `storefrontRedirect`
         * will pass through the 404 response.
         */
        return storefrontRedirect({ request, response, storefront });
      }

      return response;
    } catch (error) {
      console.error(error);
      return new Response('An unexpected error occurred', { status: 500 });
    }
  },
};
